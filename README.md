# SpeakConnect - Speaker-Event Matching Platform

A modern, minimalist landing page for connecting technical speakers with community events, starting with the Charlotte Java User Group.

## Features

- **Modern Design**: Clean, minimalist aesthetic with blue color palette
- **HTMX Integration**: Dynamic form submission without page reloads
- **Tailwind CSS**: Utility-first styling for responsive design
- **FastAPI Backend**: Lightweight Python web framework
- **Mobile Responsive**: Optimized for all device sizes
- **Accessibility**: WCAG 2.1 AA compliant design

## Technology Stack

- **Frontend**: HTML5, Tailwind CSS, HTMX
- **Backend**: FastAPI (Python)
- **Styling**: Tailwind CSS utility classes
- **Interactivity**: HTMX for dynamic behavior

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**
   ```bash
   python main.py
   ```

3. **Open in Browser**
   Navigate to `http://localhost:8000`

## Development

The application consists of:

- `index.html` - The main landing page
- `main.py` - FastAPI server with contact form handling
- `requirements.txt` - Python dependencies

### Contact Form

The contact form uses HTMX to submit data to `/contact` endpoint without page refresh. Currently, form submissions are logged to the console. In production, you would:

- Save submissions to a database
- Send email notifications
- Add proper validation and sanitization
- Implement rate limiting

## Design Specifications

- **Primary Color**: #3B82F6 (Blue)
- **Secondary Color**: #1E40AF (Dark Blue)
- **Accent Color**: #60A5FA (Light Blue)
- **Typography**: System fonts for optimal performance
- **Layout**: Mobile-first responsive design

## Page Structure

1. **Navigation**: Sticky header with login/signup buttons
2. **Hero Section**: Compelling headline with gradient background
3. **Features**: Three-column layout highlighting platform benefits
4. **How It Works**: Simple three-step process
5. **Contact Form**: HTMX-powered contact form
6. **Footer**: Links and social media icons

## Customization

To customize the design:

1. **Colors**: Modify the Tailwind config in `index.html`
2. **Content**: Update text and messaging in the HTML
3. **Styling**: Use Tailwind utility classes for design changes
4. **Functionality**: Extend the FastAPI backend in `main.py`

## Production Deployment

For production deployment:

1. Set up a proper database for contact form submissions
2. Configure email sending for notifications
3. Add environment variables for configuration
4. Set up proper logging and monitoring
5. Use a production WSGI server like Gunicorn
6. Configure reverse proxy (nginx) for static file serving

## License

This project is open source and available under the MIT License.
